"""
测试VolcanoClientService V4签名算法实现
根据故事1.9-B的AC-1验收标准
"""
import asyncio
import pytest
import json
import datetime
from unittest.mock import AsyncMock, patch, MagicMock
from api.services.volcano_client_service import VolcanoClientService
from api.settings import settings


class TestVolcanoClientV4Signature:
    """测试VolcanoClientService V4签名算法的正确性"""

    @pytest.fixture
    def volcano_service(self):
        """创建VolcanoClientService实例"""
        return VolcanoClientService()

    @pytest.mark.asyncio
    async def test_v4_signature_generation_basic(self, volcano_service):
        """
        Scenario: VolcanoClientService - 正确生成V4签名
        """
        # Arrange
        service = "ml_platform"
        host = "ark.cn-beijing.volces.com"
        region = "cn-beijing"
        method = "POST"
        path = "/api/v3/chat/completions"
        query_params = {}
        body = json.dumps({
            "model": "doubao-pro-4k",
            "messages": [{"role": "user", "content": "你好"}]
        }).encode('utf-8')

        # Act
        headers = volcano_service.get_signed_headers(
            service=service,
            host=host,
            region=region,
            method=method,
            path=path,
            query_params=query_params,
            body=body
        )

        # Assert
        assert "Authorization" in headers
        assert headers["Authorization"].startswith("HMAC-SHA256")
        assert "Credential=" in headers["Authorization"]
        assert "SignedHeaders=" in headers["Authorization"]
        assert "Signature=" in headers["Authorization"]

        assert "X-Date" in headers
        assert "X-Content-Sha256" in headers
        assert "Content-Type" in headers
        assert "Host" in headers

        # 验证UTC时间戳格式 (YYYYMMDDTHHMMSSZ)
        x_date = headers["X-Date"]
        assert len(x_date) == 16
        assert x_date.endswith("Z")
        datetime.datetime.strptime(x_date, "%Y%m%dT%H%M%SZ")

    @pytest.mark.asyncio
    async def test_v4_signature_configuration_validation(self, volcano_service):
        """
        Scenario: VolcanoClientService - 配置验证
        """
        # 测试缺少access_key
        with patch.object(settings, 'VOLCANO_ACCESS_KEY_ID', None):
            with pytest.raises(ValueError) as exc_info:
                service = VolcanoClientService()
                service.get_signed_headers(
                    service="ml_platform",
                    host="ark.cn-beijing.volces.com",
                    region="cn-beijing",
                    method="POST",
                    path="/test",
                    query_params={},
                    body=b"{}"
                )
            assert "access_key" in str(exc_info.value).lower()


class TestVolcanoClientIntegration:
    """测试VolcanoClientService与火山引擎API的集成"""

    @pytest.fixture
    def volcano_service(self):
        return VolcanoClientService()

    @pytest.mark.asyncio
    async def test_start_voice_chat_with_real_signature(self, volcano_service):
        """
        测试使用真实V4签名调用StartVoiceChat API
        验证签名能够正确生成并发送到火山引擎
        """
        from api.models.rtc_models import VolcanoAPIError

        with pytest.raises(VolcanoAPIError) as exc_info:
            await volcano_service.start_voice_chat(
                room_id="test_room_001",
                task_id="test_task_001",
                user_id="test_user_001",
                webhook_url="https://example.com/webhook",
                custom_data={"session_id": "test_session"},
                character_config=None
            )

        # 验证错误类型：400表示参数问题，401表示认证问题
        # 这证明我们的签名至少格式正确，能够到达火山引擎API
        assert exc_info.value.error_code in ["VOLCANO_API_ERROR", "VOLCANO_AUTH_ERROR"]
