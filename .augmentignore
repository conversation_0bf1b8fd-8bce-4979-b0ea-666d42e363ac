# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/
.yarn-integrity

# React Native
.expo/
.expo-shared/
dist/
web-build/
.ruff_cache/
.cursor/
.bmad-core/
# Expo
.expo/
.expo-shared/

# Metro
.metro-health-check*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
#logs
#*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Pods/

# Xcode
ios/**/*.xcodeproj
ios/**/*.xcworkspace
ios/build/

# Android
android/.gradle
android/app/build/
android/build/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
*.keystore
!debug.keystore

# Flipper
ios/Pods/Flipper
ios/Pods/Flipper-*
ios/Pods/FlipperKit

# Temporary files
tmp/
temp/

# React Native CLI
.react-native-cli/

# Bundle artifacts
*.jsbundle
*.bundle

# Watchman
.watchmanconfig

# Testing
coverage/
__tests__/coverage/

# Storybook
storybook-static/

# Optional EAS build files
eas.json
.easignore 
plan_b/
.gemini/
other_docs/
docs/all_api/
docs/volcengine